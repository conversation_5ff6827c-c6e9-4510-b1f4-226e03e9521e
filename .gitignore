# Go specific
*.exe
*.exe~
*.dll
*.so
*.dylib
gallerific

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage.html

# Go workspace file
go.work

# Build artifacts
dist/
*.AppImage

# Dependency directories
vendor/

# IDE and editor files
.vscode/
.idea/
*.sublime-*
*.vim
.emacs*

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.log
tmp/
temp/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Docker override files
docker-compose.override.yml

# Backup files
*.bak
*.backup
*~
