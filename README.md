# Gallerific

A modern photo gallery manager built with Go and Fyne, featuring an optimized Docker-based build pipeline that produces AppImages for universal Linux distribution.

## Features

- 🖼️ **Modern GUI**: Built with Fyne for native desktop experience
- 🐳 **Docker-based Builds**: No local Go installation required
- ⚡ **Optimized Build Pipeline**: Sub-minute incremental builds with aggressive caching
- 📦 **AppImage Distribution**: Universal Linux application format
- 🔄 **Hot Reload Development**: Fast iteration cycle for development

## Quick Start

### Prerequisites

- Docker Engine 20.10+ with BuildKit support
- Make (optional, but recommended)

### Build the Application

```bash
# Initial setup
make setup

# Build binary only (fast development)
make binary

# Build complete AppImage (production)
make appimage

# Quick development workflow
make quick
```

### Run the Application

```bash
# Run the binary
./dist/gallerific

# Run the AppImage
./dist/gallerific-x86_64.AppImage
```

## Build Performance

The build system is optimized for speed with aggressive caching:

- **Cold Build**: 3-5 minutes (first time)
- **Warm Build**: 30-60 seconds (cached dependencies)
- **Incremental Build**: 10-30 seconds (code changes only)

## Development

### Development Environment

```bash
# Start development shell
make dev-shell

# Start development environment
make dev-up

# Stop development environment
make dev-down
```

### Testing

```bash
# Run tests
make test

# Run tests with coverage
make test-coverage
```

### Available Make Targets

```bash
make help  # Show all available targets
```

Key targets:
- `make binary` - Build binary only
- `make appimage` - Build AppImage
- `make dev` - Fast development build
- `make test` - Run tests
- `make clean` - Clean artifacts
- `make install` - Install to system

## Project Structure

```
gallerific/
├── cmd/app/                    # Application entry point
├── internal/ui/                # UI components and logic
├── assets/                     # Icons, desktop files, resources
├── build/                      # Docker build configurations
│   ├── Dockerfile.builder      # Optimized builder image
│   ├── Dockerfile.appimage     # AppImage packaging
│   └── docker-compose.yml      # Development environment
├── scripts/                    # Build automation scripts
├── dist/                       # Build outputs (generated)
├── .dockerignore              # Optimize build context
└── Makefile                   # Build orchestration
```

## Architecture

### Build Pipeline

The build system uses a multi-stage Docker approach:

1. **Base Builder**: Pre-built image with Go toolchain and GUI dependencies
2. **Dependency Layer**: Cached Go module downloads
3. **Application Builder**: Fast incremental builds with cache mounts
4. **AppImage Packager**: Specialized container for AppImage creation
5. **Artifact Extractor**: Lightweight final stage for output delivery

### Caching Strategy

- **Docker Layer Caching**: Optimized Dockerfile layer ordering
- **Go Module Cache**: Persistent volume for module downloads
- **Build Cache**: BuildKit cache mounts for Go build artifacts
- **Incremental Builds**: Only rebuild changed components

## Technology Stack

- **Language**: Go 1.21+
- **GUI Framework**: Fyne v2.x
- **Containerization**: Docker with BuildKit
- **Packaging**: AppImage with appimagetool
- **Build Orchestration**: Make + shell scripts

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test with `make test`
5. Build with `make appimage`
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Performance Targets

- **Build Speed**: < 1 minute incremental builds
- **Cache Efficiency**: > 90% cache hit rate
- **Output Quality**: Functional AppImage under 30MB
- **Developer Experience**: Single-command build from source to AppImage
