version: '3.8'

services:
  # Development environment with live reload
  dev:
    build:
      context: ..
      dockerfile: build/Dockerfile.builder
      target: base-builder
    volumes:
      - ..:/app
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${DISPLAY}
      - CGO_ENABLED=1
    network_mode: host
    stdin_open: true
    tty: true
    command: bash
    
  # Build service for creating binaries
  build:
    build:
      context: ..
      dockerfile: build/Dockerfile.builder
      target: builder
    volumes:
      - go-mod-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - ../dist:/dist
    command: >
      sh -c "
        go build -ldflags='-s -w' -o /dist/gallerific ./cmd/app &&
        echo 'Binary built successfully at /dist/gallerific'
      "
      
  # AppImage packaging service
  appimage:
    build:
      context: ..
      dockerfile: build/Dockerfile.appimage
    volumes:
      - ../dist:/output
    depends_on:
      - build
    command: >
      sh -c "
        cp /gallerific-x86_64.AppImage /output/ &&
        echo 'AppImage created at /output/gallerific-x86_64.AppImage'
      "

volumes:
  go-mod-cache:
  go-build-cache:
