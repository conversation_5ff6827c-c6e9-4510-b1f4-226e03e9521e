# AppImage packaging Dockerfile
# First build the application
FROM golang:1.21-bullseye AS base-builder

# Install GUI development dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-dev \
    xorg-dev \
    libx11-dev \
    libxrandr-dev \
    libxinerama-dev \
    libxcursor-dev \
    libxi-dev \
    libxxf86vm-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

ENV CGO_ENABLED=1
ENV GOOS=linux
WORKDIR /app

FROM base-builder AS deps
COPY go.mod ./
COPY go.su[m] ./
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod download && go mod tidy

FROM deps AS builder
COPY . .
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go mod tidy && go build -ldflags="-s -w" -o gallerific ./cmd/app

# AppImage packaging stage
FROM ubuntu:22.04 AS appimage-builder

# Install AppImage tools and dependencies
RUN apt-get update && apt-get install -y \
    wget \
    file \
    desktop-file-utils \
    fuse \
    libfuse2 \
    && rm -rf /var/lib/apt/lists/*

# Download appimagetool
RUN wget -O /usr/local/bin/appimagetool \
    https://github.com/AppImage/AppImageKit/releases/download/continuous/appimagetool-x86_64.AppImage && \
    chmod +x /usr/local/bin/appimagetool

# Extract appimagetool (since we can't run AppImages in Docker easily)
RUN cd /tmp && \
    /usr/local/bin/appimagetool --appimage-extract && \
    mv squashfs-root /opt/appimagetool && \
    ln -sf /opt/appimagetool/AppRun /usr/local/bin/appimagetool

WORKDIR /build

# Copy build artifacts
COPY --from=builder /app/gallerific ./AppDir/usr/bin/
COPY assets/gallerific.desktop ./AppDir/
COPY assets/ ./AppDir/usr/share/icons/hicolor/256x256/apps/

# Create AppDir structure
RUN mkdir -p AppDir/usr/bin AppDir/usr/lib AppDir/usr/share/applications \
    AppDir/usr/share/icons/hicolor/256x256/apps

# Copy desktop file to proper location
RUN cp AppDir/gallerific.desktop AppDir/usr/share/applications/

# Create AppRun script
RUN echo '#!/bin/bash\n\
HERE="$(dirname "$(readlink -f "${0}")")"\n\
export PATH="${HERE}/usr/bin:${PATH}"\n\
export LD_LIBRARY_PATH="${HERE}/usr/lib:${LD_LIBRARY_PATH}"\n\
exec "${HERE}/usr/bin/gallerific" "$@"' > AppDir/AppRun && \
    chmod +x AppDir/AppRun

# Create a simple icon (placeholder)
RUN echo 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==' | base64 -d > AppDir/gallerific.png && \
    cp AppDir/gallerific.png AppDir/usr/share/icons/hicolor/256x256/apps/

# Build AppImage
RUN ARCH=x86_64 /usr/local/bin/appimagetool AppDir gallerific-x86_64.AppImage

# Final stage for artifact extraction
FROM ubuntu:22.04 AS artifacts
COPY --from=appimage-builder /build/gallerific-x86_64.AppImage /
CMD ["cat", "/gallerific-x86_64.AppImage"]
