# Makefile for Gallerific - Go Desktop Application with Docker Build Pipeline
# Optimized for fast, cached builds and AppImage distribution

.PHONY: help build binary appimage dev clean test docker-clean setup check deps

# Configuration
PROJECT_NAME := gallerific
BUILD_SCRIPT := ./scripts/build.sh
DIST_DIR := dist
BUILD_DIR := build

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m

# Help target
help: ## Show this help message
	@echo -e "$(BLUE)Gallerific Build System$(NC)"
	@echo -e "======================="
	@echo -e ""
	@echo -e "$(GREEN)Available targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo -e ""
	@echo -e "$(GREEN)Build Performance Targets:$(NC)"
	@echo -e "  • Cold build:        3-5 minutes (first time)"
	@echo -e "  • Warm build:        30-60 seconds (cached deps)"
	@echo -e "  • Incremental build: 10-30 seconds (code changes)"
	@echo -e ""
	@echo -e "$(GREEN)Examples:$(NC)"
	@echo -e "  make setup           # Initial project setup"
	@echo -e "  make dev             # Fast development build"
	@echo -e "  make appimage        # Complete AppImage build"
	@echo -e "  make clean           # Clean all artifacts"

# Setup and dependency checking
setup: ## Initial project setup and dependency check
	@echo -e "$(BLUE)[SETUP]$(NC) Initializing Gallerific build environment..."
	@$(BUILD_SCRIPT) help > /dev/null 2>&1 || (echo -e "$(RED)[ERROR]$(NC) Build script not executable" && exit 1)
	@docker --version > /dev/null 2>&1 || (echo -e "$(RED)[ERROR]$(NC) Docker not found" && exit 1)
	@echo -e "$(GREEN)[SUCCESS]$(NC) Setup completed successfully"

check: setup ## Check dependencies and environment
	@echo -e "$(BLUE)[CHECK]$(NC) Verifying build environment..."
	@docker info > /dev/null 2>&1 || (echo -e "$(RED)[ERROR]$(NC) Docker daemon not running" && exit 1)
	@echo -e "$(GREEN)[SUCCESS]$(NC) Environment check passed"

# Build targets
binary: check ## Build binary only (fast, for development)
	@echo -e "$(BLUE)[BUILD]$(NC) Building binary..."
	@$(BUILD_SCRIPT) binary
	@echo -e "$(GREEN)[SUCCESS]$(NC) Binary build completed"

appimage: check ## Build complete AppImage (production ready)
	@echo -e "$(BLUE)[BUILD]$(NC) Building AppImage..."
	@$(BUILD_SCRIPT) appimage
	@echo -e "$(GREEN)[SUCCESS]$(NC) AppImage build completed"

dev: check ## Fast development build with caching
	@echo -e "$(BLUE)[DEV]$(NC) Starting development build..."
	@$(BUILD_SCRIPT) dev
	@echo -e "$(GREEN)[SUCCESS]$(NC) Development build completed"

# Convenience aliases
build: binary ## Alias for 'binary' target

# Testing targets
test: ## Run tests with coverage report
	@echo -e "$(BLUE)[TEST]$(NC) Running tests with coverage..."
	@cd $(BUILD_DIR) && docker-compose run --rm dev go test -coverprofile=coverage.out ./...
	@cd $(BUILD_DIR) && docker-compose run --rm dev go tool cover -html=coverage.out -o coverage.html
	@echo -e "$(GREEN)[SUCCESS]$(NC) Tests completed with coverage report at build/coverage.html"

# Development environment
dev-shell: check ## Start development shell in Docker
	@echo -e "$(BLUE)[DEV]$(NC) Starting development shell..."
	@cd $(BUILD_DIR) && docker-compose run --rm dev

dev-up: check ## Start development environment
	@echo -e "$(BLUE)[DEV]$(NC) Starting development environment..."
	@cd $(BUILD_DIR) && docker-compose up -d dev

dev-down: ## Stop development environment
	@echo -e "$(BLUE)[DEV]$(NC) Stopping development environment..."
	@cd $(BUILD_DIR) && docker-compose down

# Cleaning targets
clean: ## Clean build artifacts
	@echo -e "$(BLUE)[CLEAN]$(NC) Cleaning build artifacts..."
	@$(BUILD_SCRIPT) clean
	@echo -e "$(GREEN)[SUCCESS]$(NC) Clean completed"

docker-clean: ## Clean Docker images and containers
	@echo -e "$(BLUE)[CLEAN]$(NC) Cleaning Docker artifacts..."
	@docker system prune -f
	@docker volume prune -f
	@echo -e "$(GREEN)[SUCCESS]$(NC) Docker clean completed"

deep-clean: clean docker-clean ## Complete cleanup (artifacts + Docker)
	@echo -e "$(GREEN)[SUCCESS]$(NC) Deep clean completed"

# Utility targets
size: ## Show build artifact sizes
	@echo -e "$(BLUE)[INFO]$(NC) Build artifact sizes:"
	@if [ -f "$(DIST_DIR)/$(PROJECT_NAME)" ]; then \
		echo "  Binary: $$(du -h $(DIST_DIR)/$(PROJECT_NAME) | cut -f1)"; \
	fi
	@if [ -f "$(DIST_DIR)/$(PROJECT_NAME)-x86_64.AppImage" ]; then \
		echo "  AppImage: $$(du -h $(DIST_DIR)/$(PROJECT_NAME)-x86_64.AppImage | cut -f1)"; \
	fi

info: ## Show project information
	@echo -e "$(BLUE)Project Information$(NC)"
	@echo -e "==================="
	@echo -e "Name: $(PROJECT_NAME)"
	@echo -e "Build System: Docker + Make"
	@echo -e "GUI Framework: Fyne"
	@echo -e "Target: Linux AppImage"
	@echo -e "Go Version: 1.21+"
	@echo -e ""
	@echo -e "$(BLUE)Directory Structure:$(NC)"
	@find . -type d -name ".*" -prune -o -type d -print | head -20 | sed 's/^/  /'

# Performance monitoring
benchmark: ## Run build performance benchmark
	@echo -e "$(BLUE)[BENCHMARK]$(NC) Running build performance test..."
	@echo -e "Cold build (clean + appimage):"
	@time (make clean > /dev/null 2>&1 && make appimage > /dev/null 2>&1)
	@echo -e ""
	@echo -e "Warm build (appimage with cache):"
	@time make appimage > /dev/null 2>&1
	@echo -e "$(GREEN)[SUCCESS]$(NC) Benchmark completed"

# Quick development workflow
quick: ## Quick development workflow (binary + basic test)
	@echo -e "$(BLUE)[QUICK]$(NC) Running quick development workflow..."
	@make binary
	@echo -e "$(GREEN)[SUCCESS]$(NC) Quick workflow completed"

# Production workflow
release: clean appimage size ## Complete production release workflow
	@echo -e "$(GREEN)[SUCCESS]$(NC) Release build completed"
	@echo -e ""
	@echo -e "$(BLUE)Release artifacts:$(NC)"
	@ls -la $(DIST_DIR)/

# Install target (for local development)
install: binary ## Install binary to local system (requires sudo)
	@echo -e "$(BLUE)[INSTALL]$(NC) Installing $(PROJECT_NAME) to /usr/local/bin..."
	@sudo cp $(DIST_DIR)/$(PROJECT_NAME) /usr/local/bin/
	@sudo chmod +x /usr/local/bin/$(PROJECT_NAME)
	@echo -e "$(GREEN)[SUCCESS]$(NC) $(PROJECT_NAME) installed successfully"

# Uninstall target
uninstall: ## Uninstall binary from local system (requires sudo)
	@echo -e "$(BLUE)[UNINSTALL]$(NC) Removing $(PROJECT_NAME) from /usr/local/bin..."
	@sudo rm -f /usr/local/bin/$(PROJECT_NAME)
	@echo -e "$(GREEN)[SUCCESS]$(NC) $(PROJECT_NAME) uninstalled successfully"
