#!/bin/bash

# Build script for Gallerific
# Provides fast, cached Docker-based builds

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="gallerific"
BUILD_DIR="build"
DIST_DIR="dist"
DOCKER_BUILDKIT=1

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check dependencies
check_dependencies() {
    log_info "Checking dependencies..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed or not in PATH"
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running"
        exit 1
    fi

    log_success "Dependencies check passed"
}

# Create output directory
setup_directories() {
    log_info "Setting up directories..."
    mkdir -p "$DIST_DIR"
    log_success "Directories created"
}

# Build binary only
build_binary() {
    log_info "Building binary with Docker..."

    export DOCKER_BUILDKIT=1

    docker build \
        --file "$BUILD_DIR/Dockerfile.builder" \
        --target builder \
        --tag "${PROJECT_NAME}:builder" \
        .

    # Extract binary from container
    docker create --name "${PROJECT_NAME}_extract" "${PROJECT_NAME}:builder"
    docker cp "${PROJECT_NAME}_extract:/app/${PROJECT_NAME}" "$DIST_DIR/"
    docker rm "${PROJECT_NAME}_extract"

    log_success "Binary built successfully at $DIST_DIR/$PROJECT_NAME"
}

# Build AppImage
build_appimage() {
    log_info "Building AppImage..."

    # First build the binary
    build_binary

    # Then build AppImage
    export DOCKER_BUILDKIT=1

    docker build \
        --file "$BUILD_DIR/Dockerfile.appimage" \
        --tag "${PROJECT_NAME}:appimage" \
        .

    # Extract AppImage from container
    docker create --name "${PROJECT_NAME}_appimage_extract" "${PROJECT_NAME}:appimage"
    docker cp "${PROJECT_NAME}_appimage_extract:/gallerific-x86_64.AppImage" "$DIST_DIR/"
    docker rm "${PROJECT_NAME}_appimage_extract"

    log_success "AppImage built successfully at $DIST_DIR/gallerific-x86_64.AppImage"
}

# Clean build artifacts
clean() {
    log_info "Cleaning build artifacts..."

    # Remove dist directory
    rm -rf "$DIST_DIR"

    # Remove Docker images
    docker rmi "${PROJECT_NAME}:builder" 2>/dev/null || true
    docker rmi "${PROJECT_NAME}:appimage" 2>/dev/null || true

    # Clean up any leftover containers
    docker container prune -f

    log_success "Clean completed"
}

# Development build (fast, cached)
dev_build() {
    log_info "Starting development build..."

    cd "$BUILD_DIR"
    docker compose build dev
    docker compose run --rm build
    cd ..

    log_success "Development build completed"
}

# Show usage
usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  binary     Build binary only"
    echo "  appimage   Build AppImage (includes binary)"
    echo "  dev        Fast development build"
    echo "  clean      Clean build artifacts"
    echo "  help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 binary          # Build just the binary"
    echo "  $0 appimage        # Build complete AppImage"
    echo "  $0 dev             # Fast development build"
    echo "  $0 clean           # Clean all artifacts"
}

# Main execution
main() {
    case "${1:-}" in
        binary)
            check_dependencies
            setup_directories
            build_binary
            ;;
        appimage)
            check_dependencies
            setup_directories
            build_appimage
            ;;
        dev)
            check_dependencies
            setup_directories
            dev_build
            ;;
        clean)
            clean
            ;;
        help|--help|-h)
            usage
            ;;
        "")
            log_error "No command specified"
            usage
            exit 1
            ;;
        *)
            log_error "Unknown command: $1"
            usage
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
