# Docker ignore file for optimized build context
# Reduces build context size and improves build speed

# Git and version control
.git
.gitignore
.gitattributes
.gitmodules

# Build artifacts and output
dist/
*.AppImage
gallerific
*.exe
*.dll
*.so
*.dylib

# Development files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Documentation and non-essential files
README.md
PLAN.md
LICENSE
CHANGELOG.md
docs/
*.md

# Test files and coverage
*_test.go
coverage.out
coverage.html
test_results/

# Temporary files
tmp/
temp/
*.tmp
*.log

# OS specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and editor files
.vscode/
.idea/
*.sublime-*
*.vim
.emacs*

# Dependencies (will be downloaded in container)
vendor/

# Local development environment
.env
.env.local
.env.development
.env.test
.env.production

# Docker files (except the ones we need)
docker-compose.override.yml
.dockerignore

# Backup files
*.bak
*.backup
*~
